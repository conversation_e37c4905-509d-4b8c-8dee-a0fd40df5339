#!/usr/bin/env python3
"""
AI Video Detection - PyQt5 Main Entry Point
Modern PyQt5 implementation with enhanced GUI and functionality
"""

import sys
import os

# Add current directory to path for imports
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def create_directory_structure():
    """Create necessary directories if they don't exist"""
    directories = [
        'gui_pyqt5',
        'utils', 
        'detection',
        'recording',
        'models',
        'logs',
        'reports',
        'snapshots',
        'Security_Footage'
    ]
    
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
        
        # Create __init__.py files for Python packages
        if directory in ['gui_pyqt5', 'utils', 'detection', 'recording']:
            init_file = os.path.join(directory, '__init__.py')
            if not os.path.exists(init_file):
                with open(init_file, 'w') as f:
                    f.write('# Package initialization\n')

def check_dependencies():
    """Check and report on available dependencies"""
    dependencies = {
        'PyQt5': 'PyQt5.QtWidgets',
        'opencv-python': 'cv2',
        'numpy': 'numpy',
        'pillow': 'PIL',
        'datetime': 'datetime'
    }
    
    available = []
    missing = []
    
    for package, module in dependencies.items():
        try:
            __import__(module)
            available.append(package)
        except ImportError:
            missing.append(package)
    
    print(f"✅ Available dependencies: {', '.join(available)}")
    if missing:
        print(f"⚠️ Missing dependencies: {', '.join(missing)}")
        print("📦 Install missing packages with: pip install " + " ".join(missing))
    
    return len(missing) == 0

def main():
    """Main application entry point for PyQt5 version"""
    try:
        print("🛡️ AI Video Detection Tool - PyQt5 Edition")
        print("=" * 50)
        print()
        
        # Create directory structure
        create_directory_structure()
        print("✅ Directory structure initialized")
        
        # Check dependencies
        deps_ok = check_dependencies()
        if not deps_ok:
            print("⚠️ Some dependencies are missing, but the app will try to run...")
        
        # Import PyQt5 components
        try:
            from gui_pyqt5.login_window import LoginWindow
            from gui_pyqt5.theme_system import apply_professional_theme, get_theme_manager
            from gui_pyqt5.accessibility import setup_accessibility_features, get_accessibility_manager
            print("✅ PyQt5 modules imported successfully")
        except ImportError as e:
            print(f"❌ Error importing PyQt5 modules: {e}")
            print("🔄 Trying fallback to Tkinter version...")

            try:
                # Fallback to original Tkinter version
                from gui.login_window import LoginWindow
                print("✅ Fallback to Tkinter login window")
                print("⚠️ Running in Tkinter compatibility mode")
            except ImportError:
                print("❌ No login window available")
                create_basic_pyqt5_interface()
                return

        # Initialize QApplication first
        from PyQt5.QtWidgets import QApplication

        # Create QApplication instance if it doesn't exist
        qt_app = QApplication.instance()
        if qt_app is None:
            qt_app = QApplication(sys.argv)

        # Apply professional theme and accessibility features
        try:
            print("🎨 Applying professional theme...")
            theme_manager = apply_professional_theme(qt_app, 'light')

            print("♿ Setting up accessibility features...")
            accessibility_manager = setup_accessibility_features(qt_app)

            print("✅ Professional UI enhancements applied")
        except Exception as e:
            print(f"⚠️ Warning: Could not apply UI enhancements: {e}")

        # Start login window
        print("🚀 Starting enhanced PyQt5 application...")
        login_window = LoginWindow()
        login_window.show()

        # Run the application
        return qt_app.exec_()
        
    except Exception as e:
        print(f"❌ Error starting application: {e}")
        create_emergency_pyqt5_interface()

def create_basic_pyqt5_interface():
    """Create a basic PyQt5 interface when imports fail"""
    try:
        from PyQt5.QtWidgets import (
            QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
            QLabel, QPushButton, QTextEdit, QMessageBox
        )
        from PyQt5.QtCore import Qt
        from PyQt5.QtGui import QFont
        
        app = QApplication(sys.argv)
        
        # Main window
        window = QMainWindow()
        window.setWindowTitle("AI Video Detection - PyQt5 Basic Mode")
        window.setGeometry(100, 100, 700, 500)
        window.setStyleSheet("background-color: #E8F4FD;")
        
        # Central widget
        central_widget = QWidget()
        window.setCentralWidget(central_widget)
        
        # Main layout
        layout = QVBoxLayout(central_widget)
        layout.setContentsMargins(30, 30, 30, 30)
        layout.setSpacing(20)
        
        # Title
        title = QLabel("🛡️ AI Video Detection")
        title.setFont(QFont('Arial', 20, QFont.Bold))
        title.setStyleSheet("color: #2C3E50; margin-bottom: 10px;")
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)
        
        # Status
        status = QLabel("⚠️ Tool in Basic PyQt5 Mode\nSome features may be limited")
        status.setFont(QFont('Arial', 12))
        status.setStyleSheet("color: #E67E22; margin-bottom: 20px;")
        status.setAlignment(Qt.AlignCenter)
        layout.addWidget(status)
        
        # Camera test button
        def test_camera():
            try:
                import cv2
                cap = cv2.VideoCapture(0)
                if cap.isOpened():
                    ret, frame = cap.read()
                    if ret:
                        QMessageBox.information(window, "Success", 
                                              f"✅ Camera is working!\n\n"
                                              f"Resolution: {frame.shape[1]}x{frame.shape[0]}\n"
                                              f"Status: Ready for detection")
                    else:
                        QMessageBox.warning(window, "Warning", 
                                          "⚠️ Camera detected but cannot read frames")
                    cap.release()
                else:
                    QMessageBox.critical(window, "Error", 
                                       "❌ No camera detected\n\nPlease check camera connection")
            except ImportError:
                QMessageBox.critical(window, "Error", 
                                   "❌ OpenCV not available\n\nInstall with: pip install opencv-python")
        
        camera_btn = QPushButton("📷 Test Camera")
        camera_btn.setFont(QFont('Arial', 12, QFont.Bold))
        camera_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498DB;
                color: white;
                border: none;
                padding: 12px 24px;
                border-radius: 6px;
            }
            QPushButton:hover {
                background-color: #5DADE2;
            }
            QPushButton:pressed {
                background-color: #2980B9;
            }
        """)
        camera_btn.clicked.connect(test_camera)
        layout.addWidget(camera_btn)
        
        # Info text
        info_text = """🎯 AI Video Detection Features:
• Real-time facial expression detection with PyQt5
• Advanced age estimation and analysis
• Intelligent object detection and tracking
• Security anomaly monitoring and alerts
• Comprehensive analytics dashboard
• Professional PyQt5 user interface

📋 System Status:
• Core system: Operational
• PyQt5 GUI: Active
• Camera support: Testing available
• AI models: Loading...
• Database: Initializing...

🔧 To access full PyQt5 features:
1. Install dependencies: pip install PyQt5 opencv-python numpy pillow
2. Download AI models: python download_models.py
3. Restart application with: python main_pyqt5.py"""
        
        info_display = QTextEdit()
        info_display.setPlainText(info_text)
        info_display.setFont(QFont('Arial', 10))
        info_display.setStyleSheet("""
            QTextEdit {
                background-color: white;
                border: 1px solid #BDC3C7;
                border-radius: 6px;
                padding: 15px;
                color: #2C3E50;
            }
        """)
        info_display.setReadOnly(True)
        layout.addWidget(info_display)
        
        # Button layout
        button_layout = QHBoxLayout()
        
        # Full app button
        def start_full_app():
            try:
                from gui_pyqt5.login_window import LoginWindow
                window.close()
                login = LoginWindow()
                login.show()
            except ImportError:
                QMessageBox.warning(window, "Warning", 
                                  "⚠️ Full PyQt5 application not available\n\n"
                                  "Please ensure all modules are properly installed")
        
        full_app_btn = QPushButton("🚀 Start Full App")
        full_app_btn.setFont(QFont('Arial', 11, QFont.Bold))
        full_app_btn.setStyleSheet("""
            QPushButton {
                background-color: #27AE60;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 6px;
            }
            QPushButton:hover {
                background-color: #2ECC71;
            }
        """)
        full_app_btn.clicked.connect(start_full_app)
        button_layout.addWidget(full_app_btn)
        
        # Exit button
        exit_btn = QPushButton("🚪 Exit")
        exit_btn.setFont(QFont('Arial', 11, QFont.Bold))
        exit_btn.setStyleSheet("""
            QPushButton {
                background-color: #E74C3C;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 6px;
            }
            QPushButton:hover {
                background-color: #EC7063;
            }
        """)
        exit_btn.clicked.connect(window.close)
        button_layout.addWidget(exit_btn)
        
        layout.addLayout(button_layout)
        
        # Show window
        window.show()
        
        # Center window
        screen = app.desktop().screenGeometry()
        window_geometry = window.frameGeometry()
        center_point = screen.center()
        window_geometry.moveCenter(center_point)
        window.move(window_geometry.topLeft())
        
        return app.exec_()
        
    except ImportError:
        print("❌ PyQt5 not available for basic interface")
        create_emergency_tkinter_interface()
    except Exception as e:
        print(f"❌ Error creating basic PyQt5 interface: {e}")
        create_emergency_tkinter_interface()

def create_emergency_pyqt5_interface():
    """Emergency PyQt5 interface when everything fails"""
    try:
        from PyQt5.QtWidgets import QApplication, QMessageBox
        
        app = QApplication(sys.argv)
        
        msg = QMessageBox()
        msg.setWindowTitle("AI Video Detection - Emergency Mode")
        msg.setIcon(QMessageBox.Critical)
        msg.setText("⚠️ AI Video Detection Tool\nEmergency Mode")
        msg.setInformativeText(
            "Please check:\n"
            "1. PyQt5 installation: pip install PyQt5\n"
            "2. Python installation\n"
            "3. Required packages\n"
            "4. File permissions"
        )
        msg.exec_()
        
    except ImportError:
        create_emergency_tkinter_interface()
    except Exception as e:
        print(f"❌ Emergency PyQt5 interface failed: {e}")
        create_emergency_tkinter_interface()

def create_emergency_tkinter_interface():
    """Emergency Tkinter interface as last resort"""
    try:
        import tkinter as tk
        from tkinter import messagebox
        
        root = tk.Tk()
        root.withdraw()  # Hide main window
        
        messagebox.showerror(
            "AI Video Detection - Emergency",
            "⚠️ Critical Error\n\n"
            "PyQt5 is not available.\n\n"
            "Please install PyQt5:\n"
            "pip install PyQt5>=5.15.0\n\n"
            "Or run the Tkinter version:\n"
            "python main.py"
        )
        
        root.destroy()
        
    except ImportError:
        print("❌ Critical error: No GUI framework available")
        print("Please install PyQt5 or Tkinter")
        input("Press Enter to exit...")

if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code or 0)
    except KeyboardInterrupt:
        print("\n🛑 Application interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        sys.exit(1)
