"""
Professional Theme System for AI Video Detection

This module provides enterprise-grade theming with:
- Consistent color schemes
- Professional typography
- Accessibility compliance
- Dark/Light mode support
- Responsive design principles
"""

from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QObject, pyqtSignal
from PyQt5.QtGui import QPalette, QColor

from .base_components import Colors, Typography


class ThemeManager(QObject):
    """
    Central theme manager for the application
    Handles theme switching, accessibility, and consistent styling
    """
    
    theme_changed = pyqtSignal(str)  # Emitted when theme changes
    
    def __init__(self):
        super().__init__()
        self.current_theme = 'light'
        self.accessibility_mode = False
        self.high_contrast = False
        self.font_scale = 1.0
        
    def apply_light_theme(self, app=None):
        """Apply professional light theme"""
        if app is None:
            app = QApplication.instance()
        
        if app:
            # Set application-wide stylesheet
            stylesheet = self.get_light_theme_stylesheet()
            app.setStyleSheet(stylesheet)
            
            # Set palette for native widgets
            palette = self.get_light_palette()
            app.setPalette(palette)
            
        self.current_theme = 'light'
        self.theme_changed.emit('light')
    
    def apply_dark_theme(self, app=None):
        """Apply professional dark theme"""
        if app is None:
            app = QApplication.instance()
        
        if app:
            # Set application-wide stylesheet
            stylesheet = self.get_dark_theme_stylesheet()
            app.setStyleSheet(stylesheet)
            
            # Set palette for native widgets
            palette = self.get_dark_palette()
            app.setPalette(palette)
            
        self.current_theme = 'dark'
        self.theme_changed.emit('dark')
    
    def toggle_theme(self):
        """Toggle between light and dark themes"""
        if self.current_theme == 'light':
            self.apply_dark_theme()
        else:
            self.apply_light_theme()
    
    def enable_accessibility_mode(self, enabled=True):
        """Enable accessibility features"""
        self.accessibility_mode = enabled
        
        if enabled:
            # Increase font sizes
            self.font_scale = 1.2
            # Enable high contrast
            self.high_contrast = True
        else:
            self.font_scale = 1.0
            self.high_contrast = False
        
        # Reapply current theme with accessibility settings
        if self.current_theme == 'light':
            self.apply_light_theme()
        else:
            self.apply_dark_theme()
    
    def get_light_theme_stylesheet(self):
        """Get comprehensive light theme stylesheet"""
        font_size_body = int(Typography.FONT_SIZE_BODY * self.font_scale)
        font_size_heading = int(Typography.FONT_SIZE_HEADING * self.font_scale)
        
        return f"""
        /* Global Application Styling */
        QApplication {{
            font-family: {Typography.FONT_FAMILY};
            font-size: {font_size_body}px;
        }}
        
        /* Main Windows */
        QMainWindow {{
            background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 1,
                                      stop: 0 {Colors.BACKGROUND},
                                      stop: 1 {Colors.GRAY_50});
            color: {Typography.TEXT_PRIMARY};
        }}
        
        /* Buttons */
        QPushButton {{
            background-color: {Colors.PRIMARY};
            color: {Colors.WHITE};
            border: none;
            border-radius: 8px;
            padding: 8px 16px;
            font-weight: 500;
            font-size: {font_size_body}px;
            min-height: 32px;
        }}
        
        QPushButton:hover {{
            background-color: {Colors.PRIMARY_LIGHT};
            transform: translateY(-1px);
        }}
        
        QPushButton:pressed {{
            background-color: {Colors.PRIMARY_DARK};
        }}
        
        QPushButton:disabled {{
            background-color: {Colors.GRAY_300};
            color: {Colors.GRAY_500};
        }}
        
        /* Input Fields */
        QLineEdit {{
            background-color: {Colors.WHITE};
            border: 2px solid {Colors.GRAY_300};
            border-radius: 8px;
            padding: 8px 12px;
            font-size: {font_size_body}px;
            selection-background-color: {Colors.PRIMARY_LIGHT};
        }}
        
        QLineEdit:focus {{
            border-color: {Colors.PRIMARY};
            outline: none;
        }}
        
        QLineEdit:disabled {{
            background-color: {Colors.GRAY_100};
            color: {Colors.GRAY_500};
        }}
        
        /* Text Areas */
        QTextEdit {{
            background-color: {Colors.WHITE};
            border: 2px solid {Colors.GRAY_300};
            border-radius: 8px;
            padding: 12px;
            font-size: {font_size_body}px;
            selection-background-color: {Colors.PRIMARY_LIGHT};
        }}
        
        QTextEdit:focus {{
            border-color: {Colors.PRIMARY};
        }}
        
        /* Labels */
        QLabel {{
            color: {Typography.TEXT_PRIMARY};
            font-size: {font_size_body}px;
        }}
        
        /* Frames */
        QFrame {{
            background-color: {Colors.WHITE};
            border: 1px solid {Colors.GRAY_200};
            border-radius: 8px;
        }}
        
        /* Tabs */
        QTabWidget::pane {{
            border: 2px solid {Colors.GRAY_300};
            border-radius: 8px;
            background-color: {Colors.WHITE};
        }}
        
        QTabBar::tab {{
            background-color: {Colors.GRAY_100};
            border: 2px solid {Colors.GRAY_300};
            border-bottom: none;
            border-radius: 8px 8px 0 0;
            padding: 8px 16px;
            margin-right: 2px;
            font-size: {font_size_body}px;
            font-weight: 500;
        }}
        
        QTabBar::tab:selected {{
            background-color: {Colors.WHITE};
            border-color: {Colors.PRIMARY};
            color: {Colors.PRIMARY};
        }}
        
        QTabBar::tab:hover {{
            background-color: {Colors.PRIMARY_LIGHT};
            color: {Colors.WHITE};
        }}
        
        /* Scrollbars */
        QScrollBar:vertical {{
            background-color: {Colors.GRAY_100};
            width: 12px;
            border-radius: 6px;
            margin: 0px;
        }}
        
        QScrollBar::handle:vertical {{
            background-color: {Colors.GRAY_400};
            border-radius: 6px;
            min-height: 24px;
            margin: 2px;
        }}
        
        QScrollBar::handle:vertical:hover {{
            background-color: {Colors.GRAY_500};
        }}
        
        QScrollBar::handle:vertical:pressed {{
            background-color: {Colors.GRAY_600};
        }}
        
        QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {{
            border: none;
            background: none;
        }}
        
        /* Status Bar */
        QStatusBar {{
            background-color: {Colors.GRAY_100};
            border-top: 1px solid {Colors.GRAY_300};
            padding: 4px;
            font-size: {font_size_body}px;
        }}
        
        /* Menu Bar */
        QMenuBar {{
            background-color: {Colors.WHITE};
            border-bottom: 1px solid {Colors.GRAY_300};
            padding: 4px;
            font-size: {font_size_body}px;
        }}
        
        QMenuBar::item {{
            background-color: transparent;
            padding: 8px 12px;
            border-radius: 4px;
        }}
        
        QMenuBar::item:selected {{
            background-color: {Colors.PRIMARY_LIGHT};
            color: {Colors.WHITE};
        }}
        
        /* Tooltips */
        QToolTip {{
            background-color: {Colors.GRAY_800};
            color: {Colors.WHITE};
            border: 1px solid {Colors.GRAY_600};
            border-radius: 4px;
            padding: 8px;
            font-size: {font_size_body}px;
        }}
        
        /* Checkboxes */
        QCheckBox {{
            font-size: {font_size_body}px;
            spacing: 8px;
        }}
        
        QCheckBox::indicator {{
            width: 18px;
            height: 18px;
            border: 2px solid {Colors.GRAY_400};
            border-radius: 4px;
            background-color: {Colors.WHITE};
        }}
        
        QCheckBox::indicator:checked {{
            background-color: {Colors.PRIMARY};
            border-color: {Colors.PRIMARY};
            image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIiIGhlaWdodD0iOSIgdmlld0JveD0iMCAwIDEyIDkiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxwYXRoIGQ9Ik0xIDQuNUw0LjUgOEwxMSAxIiBzdHJva2U9IndoaXRlIiBzdHJva2Utd2lkdGg9IjIiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIvPgo8L3N2Zz4K);
        }}
        
        QCheckBox::indicator:hover {{
            border-color: {Colors.PRIMARY};
        }}
        """
    
    def get_dark_theme_stylesheet(self):
        """Get comprehensive dark theme stylesheet"""
        font_size_body = int(Typography.FONT_SIZE_BODY * self.font_scale)
        
        return f"""
        /* Dark Theme Global Styling */
        QApplication {{
            font-family: {Typography.FONT_FAMILY};
            font-size: {font_size_body}px;
        }}
        
        QMainWindow {{
            background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 1,
                                      stop: 0 {Colors.GRAY_900},
                                      stop: 1 {Colors.GRAY_800});
            color: {Colors.WHITE};
        }}
        
        QPushButton {{
            background-color: {Colors.PRIMARY};
            color: {Colors.WHITE};
            border: none;
            border-radius: 8px;
            padding: 8px 16px;
            font-weight: 500;
            font-size: {font_size_body}px;
            min-height: 32px;
        }}
        
        QPushButton:hover {{
            background-color: {Colors.PRIMARY_LIGHT};
        }}
        
        QLineEdit {{
            background-color: {Colors.GRAY_700};
            border: 2px solid {Colors.GRAY_600};
            border-radius: 8px;
            padding: 8px 12px;
            color: {Colors.WHITE};
            font-size: {font_size_body}px;
        }}
        
        QLineEdit:focus {{
            border-color: {Colors.PRIMARY};
        }}
        
        QTextEdit {{
            background-color: {Colors.GRAY_700};
            border: 2px solid {Colors.GRAY_600};
            border-radius: 8px;
            padding: 12px;
            color: {Colors.WHITE};
            font-size: {font_size_body}px;
        }}
        
        QLabel {{
            color: {Colors.WHITE};
            font-size: {font_size_body}px;
        }}
        
        QFrame {{
            background-color: {Colors.GRAY_800};
            border: 1px solid {Colors.GRAY_600};
            border-radius: 8px;
        }}
        """
    
    def get_light_palette(self):
        """Get light theme palette for native widgets"""
        palette = QPalette()
        
        # Window colors
        palette.setColor(QPalette.Window, QColor(Colors.BACKGROUND))
        palette.setColor(QPalette.WindowText, QColor(Typography.TEXT_PRIMARY))
        
        # Base colors (for input fields)
        palette.setColor(QPalette.Base, QColor(Colors.WHITE))
        palette.setColor(QPalette.AlternateBase, QColor(Colors.GRAY_50))
        
        # Text colors
        palette.setColor(QPalette.Text, QColor(Typography.TEXT_PRIMARY))
        palette.setColor(QPalette.BrightText, QColor(Colors.WHITE))
        
        # Button colors
        palette.setColor(QPalette.Button, QColor(Colors.PRIMARY))
        palette.setColor(QPalette.ButtonText, QColor(Colors.WHITE))
        
        # Highlight colors
        palette.setColor(QPalette.Highlight, QColor(Colors.PRIMARY))
        palette.setColor(QPalette.HighlightedText, QColor(Colors.WHITE))
        
        return palette
    
    def get_dark_palette(self):
        """Get dark theme palette for native widgets"""
        palette = QPalette()
        
        # Window colors
        palette.setColor(QPalette.Window, QColor(Colors.GRAY_800))
        palette.setColor(QPalette.WindowText, QColor(Colors.WHITE))
        
        # Base colors
        palette.setColor(QPalette.Base, QColor(Colors.GRAY_700))
        palette.setColor(QPalette.AlternateBase, QColor(Colors.GRAY_600))
        
        # Text colors
        palette.setColor(QPalette.Text, QColor(Colors.WHITE))
        palette.setColor(QPalette.BrightText, QColor(Colors.WHITE))
        
        # Button colors
        palette.setColor(QPalette.Button, QColor(Colors.PRIMARY))
        palette.setColor(QPalette.ButtonText, QColor(Colors.WHITE))
        
        # Highlight colors
        palette.setColor(QPalette.Highlight, QColor(Colors.PRIMARY))
        palette.setColor(QPalette.HighlightedText, QColor(Colors.WHITE))
        
        return palette


# Global theme manager instance
_theme_manager = None

def get_theme_manager():
    """Get or create global theme manager"""
    global _theme_manager
    if _theme_manager is None:
        _theme_manager = ThemeManager()
    return _theme_manager

def apply_professional_theme(app=None, theme='light'):
    """Apply professional theme to the application"""
    theme_manager = get_theme_manager()
    
    if theme == 'dark':
        theme_manager.apply_dark_theme(app)
    else:
        theme_manager.apply_light_theme(app)
    
    return theme_manager
