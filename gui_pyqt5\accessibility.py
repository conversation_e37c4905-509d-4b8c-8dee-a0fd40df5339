"""
Accessibility Features for AI Video Detection

This module provides comprehensive accessibility support including:
- Keyboard navigation
- Screen reader support
- High contrast mode
- Font scaling
- Focus management
- ARIA-like attributes for Qt
"""

from PyQt5.QtWidgets import QWidget, QApplication, QShortcut
from PyQt5.QtCore import Qt, QObject, pyqtSignal
from PyQt5.QtGui import QKeySequence, QFont

from .base_components import Colors, Typography


class AccessibilityManager(QObject):
    """
    Central accessibility manager for the application
    Handles keyboard navigation, screen reader support, and accessibility features
    """
    
    focus_changed = pyqtSignal(QWidget)
    accessibility_enabled = pyqtSignal(bool)
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.enabled = False
        self.high_contrast = False
        self.font_scale = 1.0
        self.keyboard_navigation = True
        self.screen_reader_support = True
        
        # Track focus for keyboard navigation
        self.focus_history = []
        self.current_focus_index = -1
        
    def enable_accessibility(self, enabled=True):
        """Enable or disable accessibility features"""
        self.enabled = enabled
        
        if enabled:
            self.setup_keyboard_navigation()
            self.setup_screen_reader_support()
            self.enable_high_contrast()
            self.setup_focus_management()
        
        self.accessibility_enabled.emit(enabled)
    
    def setup_keyboard_navigation(self):
        """Setup comprehensive keyboard navigation"""
        app = QApplication.instance()
        if not app:
            return
        
        # Global keyboard shortcuts for accessibility
        shortcuts = [
            # Navigation shortcuts
            (QKeySequence("Ctrl+Tab"), self.next_widget),
            (QKeySequence("Ctrl+Shift+Tab"), self.previous_widget),
            (QKeySequence("Alt+F4"), self.close_current_window),
            
            # Accessibility shortcuts
            (QKeySequence("Ctrl+Alt+H"), self.toggle_high_contrast),
            (QKeySequence("Ctrl+Plus"), self.increase_font_size),
            (QKeySequence("Ctrl+Minus"), self.decrease_font_size),
            (QKeySequence("Ctrl+0"), self.reset_font_size),
            
            # Application shortcuts
            (QKeySequence("F1"), self.show_help),
            (QKeySequence("Escape"), self.cancel_current_action),
            (QKeySequence("Ctrl+Q"), self.quit_application),
        ]
        
        for key_sequence, callback in shortcuts:
            shortcut = QShortcut(key_sequence, app.activeWindow() or app)
            shortcut.activated.connect(callback)
    
    def setup_screen_reader_support(self):
        """Setup screen reader support with descriptive text"""
        app = QApplication.instance()
        if not app:
            return
        
        # Set application properties for screen readers
        app.setApplicationName("AI Video Detection Tool")
        app.setApplicationDisplayName("AI Video Detection - Professional Security Monitoring")
        app.setApplicationVersion("2.0")
        app.setOrganizationName("AI Security Solutions")
        
        # Enable accessibility attributes
        app.setAttribute(Qt.AA_SynthesizeMouseForUnhandledTabletEvents, True)
    
    def setup_focus_management(self):
        """Setup intelligent focus management"""
        app = QApplication.instance()
        if not app:
            return
        
        # Connect to focus change events
        app.focusChanged.connect(self.on_focus_changed)
    
    def on_focus_changed(self, old_widget, new_widget):
        """Handle focus changes for accessibility"""
        if new_widget:
            # Add to focus history
            if new_widget not in self.focus_history:
                self.focus_history.append(new_widget)
            
            # Update current focus index
            try:
                self.current_focus_index = self.focus_history.index(new_widget)
            except ValueError:
                pass
            
            # Announce focus change for screen readers
            self.announce_widget_focus(new_widget)
            
            # Emit signal for other components
            self.focus_changed.emit(new_widget)
    
    def announce_widget_focus(self, widget):
        """Announce widget focus for screen readers"""
        if not widget or not self.screen_reader_support:
            return
        
        # Get widget description
        description = self.get_widget_description(widget)
        
        # Set accessible description
        if description:
            widget.setAccessibleDescription(description)
            
        # Set accessible name if not already set
        if not widget.accessibleName():
            name = self.get_widget_accessible_name(widget)
            if name:
                widget.setAccessibleName(name)
    
    def get_widget_description(self, widget):
        """Get descriptive text for a widget"""
        widget_type = type(widget).__name__
        
        descriptions = {
            'QPushButton': f"Button: {widget.text() or 'Unnamed button'}",
            'QLineEdit': f"Text input: {widget.placeholderText() or 'Text field'}",
            'QTextEdit': "Text area for multi-line input",
            'QLabel': f"Label: {widget.text() or 'Information display'}",
            'QCheckBox': f"Checkbox: {widget.text() or 'Option'} - {'Checked' if widget.isChecked() else 'Unchecked'}",
            'QTabWidget': "Tab container with multiple pages",
            'QScrollArea': "Scrollable content area",
            'QFrame': "Content frame or section",
        }
        
        base_description = descriptions.get(widget_type, f"{widget_type} widget")
        
        # Add state information
        if hasattr(widget, 'isEnabled') and not widget.isEnabled():
            base_description += " (disabled)"
        
        if hasattr(widget, 'toolTip') and widget.toolTip():
            base_description += f" - {widget.toolTip()}"
        
        return base_description
    
    def get_widget_accessible_name(self, widget):
        """Get accessible name for a widget"""
        # Try to get text content
        if hasattr(widget, 'text') and widget.text():
            return widget.text()
        
        # Try to get placeholder text
        if hasattr(widget, 'placeholderText') and widget.placeholderText():
            return widget.placeholderText()
        
        # Try to get object name
        if widget.objectName():
            return widget.objectName().replace('_', ' ').title()
        
        # Default to widget type
        return type(widget).__name__
    
    def next_widget(self):
        """Navigate to next focusable widget"""
        app = QApplication.instance()
        if not app:
            return
        
        current_widget = app.focusWidget()
        if current_widget:
            # Find next focusable widget
            next_widget = current_widget.nextInFocusChain()
            if next_widget and next_widget != current_widget:
                next_widget.setFocus(Qt.TabFocusReason)
    
    def previous_widget(self):
        """Navigate to previous focusable widget"""
        app = QApplication.instance()
        if not app:
            return
        
        current_widget = app.focusWidget()
        if current_widget:
            # Find previous focusable widget
            prev_widget = current_widget.previousInFocusChain()
            if prev_widget and prev_widget != current_widget:
                prev_widget.setFocus(Qt.BacktabFocusReason)
    
    def toggle_high_contrast(self):
        """Toggle high contrast mode"""
        self.high_contrast = not self.high_contrast
        
        # Apply high contrast theme
        from .theme_system import get_theme_manager
        theme_manager = get_theme_manager()
        theme_manager.high_contrast = self.high_contrast
        
        # Reapply current theme
        if theme_manager.current_theme == 'light':
            theme_manager.apply_light_theme()
        else:
            theme_manager.apply_dark_theme()
    
    def increase_font_size(self):
        """Increase font size for better readability"""
        self.font_scale = min(2.0, self.font_scale + 0.1)
        self.apply_font_scaling()
    
    def decrease_font_size(self):
        """Decrease font size"""
        self.font_scale = max(0.8, self.font_scale - 0.1)
        self.apply_font_scaling()
    
    def reset_font_size(self):
        """Reset font size to default"""
        self.font_scale = 1.0
        self.apply_font_scaling()
    
    def apply_font_scaling(self):
        """Apply font scaling to the application"""
        from .theme_system import get_theme_manager
        theme_manager = get_theme_manager()
        theme_manager.font_scale = self.font_scale
        
        # Reapply current theme with new font scale
        if theme_manager.current_theme == 'light':
            theme_manager.apply_light_theme()
        else:
            theme_manager.apply_dark_theme()
    
    def enable_high_contrast(self):
        """Enable high contrast mode for better visibility"""
        self.high_contrast = True
        self.toggle_high_contrast()
    
    def close_current_window(self):
        """Close the current active window"""
        app = QApplication.instance()
        if app and app.activeWindow():
            app.activeWindow().close()
    
    def show_help(self):
        """Show help information"""
        # This would typically show a help dialog
        print("🆘 Help: Accessibility features enabled")
        print("Keyboard shortcuts:")
        print("  Ctrl+Tab: Next widget")
        print("  Ctrl+Shift+Tab: Previous widget")
        print("  Ctrl+Alt+H: Toggle high contrast")
        print("  Ctrl++: Increase font size")
        print("  Ctrl+-: Decrease font size")
        print("  Ctrl+0: Reset font size")
        print("  F1: Show this help")
        print("  Escape: Cancel current action")
    
    def cancel_current_action(self):
        """Cancel current action or close dialogs"""
        app = QApplication.instance()
        if app:
            # Send escape key event to active widget
            active_widget = app.activeWindow()
            if active_widget:
                # Try to close modal dialogs first
                for widget in app.allWidgets():
                    if widget.isModal() and widget.isVisible():
                        widget.close()
                        return
    
    def quit_application(self):
        """Safely quit the application"""
        app = QApplication.instance()
        if app:
            app.quit()


def setup_accessibility_features(app=None):
    """Setup accessibility features for the application"""
    if app is None:
        app = QApplication.instance()
    
    if not app:
        return None
    
    # Create accessibility manager
    accessibility_manager = AccessibilityManager()
    accessibility_manager.enable_accessibility(True)
    
    # Set application properties for accessibility
    app.setApplicationName("AI Video Detection")
    app.setApplicationDisplayName("AI Video Detection - Professional Security Monitoring")
    app.setOrganizationName("AI Security Solutions")
    
    return accessibility_manager


def make_widget_accessible(widget, name=None, description=None, role=None):
    """Make a widget more accessible"""
    if name:
        widget.setAccessibleName(name)
    
    if description:
        widget.setAccessibleDescription(description)
    
    # Set focus policy for keyboard navigation
    if hasattr(widget, 'setFocusPolicy'):
        widget.setFocusPolicy(Qt.TabFocus)
    
    # Enable tooltips for additional context
    if description and not widget.toolTip():
        widget.setToolTip(description)


# Global accessibility manager instance
_accessibility_manager = None

def get_accessibility_manager():
    """Get or create global accessibility manager"""
    global _accessibility_manager
    if _accessibility_manager is None:
        _accessibility_manager = AccessibilityManager()
    return _accessibility_manager
